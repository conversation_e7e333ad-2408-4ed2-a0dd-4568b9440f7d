#!/usr/bin/env node

/**
 * مثال بسيط على خادم MCP مخصص
 * هذا المثال يوضح كيفية إنشاء أداة مخصصة لـ Gemini Code Assist
 */

const express = require('express');
const db = require('./database-config');
const app = express();
const port = process.env.PORT || 3001;

// إعداد middleware
app.use(express.json());

// إعداد CORS للسماح بالطلبات من VS Code
app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.sendStatus(200);
    } else {
        next();
    }
});

// نقطة نهاية للتحقق من حالة الخادم
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        message: 'خادم MCP يعمل بشكل طبيعي',
        timestamp: new Date().toISOString()
    });
});

// نقطة نهاية MCP الرئيسية
app.post('/mcp', async (req, res) => {
    const { method, params } = req.body;

    try {
        switch (method) {
            case 'tools/list':
                res.json({
                    tools: [
                        {
                            name: 'code_analyzer',
                            description: 'تحليل الكود وتقديم اقتراحات للتحسين',
                            inputSchema: {
                                type: 'object',
                                properties: {
                                    code: { type: 'string', description: 'الكود المراد تحليله' },
                                    language: { type: 'string', description: 'لغة البرمجة' }
                                },
                                required: ['code']
                            }
                        },
                        {
                            name: 'comment_translator',
                            description: 'ترجمة التعليقات بين العربية والإنجليزية',
                            inputSchema: {
                                type: 'object',
                                properties: {
                                    text: { type: 'string', description: 'النص المراد ترجمته' },
                                    targetLanguage: { type: 'string', description: 'اللغة المستهدفة (ar/en)' }
                                },
                                required: ['text', 'targetLanguage']
                            }
                        },
                        {
                            name: 'save_analysis',
                            description: 'حفظ نتائج تحليل الكود في قاعدة البيانات',
                            inputSchema: {
                                type: 'object',
                                properties: {
                                    projectId: { type: 'number', description: 'معرف المشروع' },
                                    filePath: { type: 'string', description: 'مسار الملف' },
                                    code: { type: 'string', description: 'الكود' },
                                    language: { type: 'string', description: 'لغة البرمجة' },
                                    analysis: { type: 'object', description: 'نتائج التحليل' }
                                },
                                required: ['projectId', 'filePath', 'code', 'language', 'analysis']
                            }
                        },
                        {
                            name: 'get_project_stats',
                            description: 'الحصول على إحصائيات المشروع',
                            inputSchema: {
                                type: 'object',
                                properties: {
                                    projectId: { type: 'number', description: 'معرف المشروع' }
                                },
                                required: ['projectId']
                            }
                        }
                    ]
                });
                break;
                
            case 'tools/call': {
                const { name, arguments: args } = params;
                
                if (name === 'code_analyzer') {
                    const analysis = await analyzeCodeWithDB(args.code, args.language, args.projectId);
                    res.json({ content: [{ type: 'text', text: analysis }] });
                } else if (name === 'comment_translator') {
                    const translation = await translateCommentWithDB(args.text, args.targetLanguage);
                    res.json({ content: [{ type: 'text', text: translation }] });
                } else if (name === 'save_analysis') {
                    const result = await saveAnalysisToDB(args);
                    res.json({ content: [{ type: 'text', text: result }] });
                } else if (name === 'get_project_stats') {
                    const stats = await getProjectStatsFromDB(args.projectId);
                    res.json({ content: [{ type: 'text', text: stats }] });
                } else {
                    res.status(400).json({ error: `أداة غير معروفة: ${name}` });
                }
                break;
            }
                
            default:
                res.status(400).json({ error: `طريقة غير مدعومة: ${method}` });
        }
    } catch (error) {
        console.error('خطأ في معالجة الطلب:', error);
        res.status(500).json({ error: 'خطأ داخلي في الخادم' });
    }
});

// دالة تحليل الكود (مثال بسيط)
function analyzeCode(code, language = 'javascript') {
    const suggestions = [];
    
    // فحوصات بسيطة
    if (code.includes('var ')) {
        suggestions.push('💡 استخدم let أو const بدلاً من var');
    }
    
    if (code.includes('console.log') && !code.includes('// debug')) {
        suggestions.push('🔍 تأكد من إزالة console.log قبل الإنتاج');
    }
    
    if (code.length > 1000) {
        suggestions.push('📏 الكود طويل، فكر في تقسيمه إلى دوال أصغر');
    }
    
    const lines = code.split('\n');
    if (lines.some(line => line.length > 100)) {
        suggestions.push('📐 بعض الأسطر طويلة جداً، فكر في تقسيمها');
    }
    
    if (suggestions.length === 0) {
        return '✅ الكود يبدو جيداً! لا توجد اقتراحات للتحسين.';
    }
    
    const formattedSuggestions = suggestions.map(s => `• ${s}`).join('\n');
    return `تحليل الكود:\n\n${formattedSuggestions}`;
}

// دالة ترجمة التعليقات (مثال بسيط)
function translateComment(text, targetLanguage) {
    // هذا مثال بسيط - في التطبيق الحقيقي ستحتاج إلى API ترجمة
    const translations = {
        'ar': {
            'function': 'دالة',
            'variable': 'متغير',
            'class': 'فئة',
            'method': 'طريقة',
            'return': 'إرجاع',
            'parameter': 'معامل'
        },
        'en': {
            'دالة': 'function',
            'متغير': 'variable',
            'فئة': 'class',
            'طريقة': 'method',
            'إرجاع': 'return',
            'معامل': 'parameter'
        }
    };
    
    let result = text;
    const dict = translations[targetLanguage] || {};
    
    Object.keys(dict).forEach(key => {
        const regex = new RegExp(key, 'gi');
        result = result.replace(regex, dict[key]);
    });
    
    return `الترجمة إلى ${targetLanguage === 'ar' ? 'العربية' : 'الإنجليزية'}:\n${result}`;
}

// دالة تحليل الكود مع حفظ في قاعدة البيانات
async function analyzeCodeWithDB(code, language = 'javascript', projectId = null) {
    const analysis = analyzeCode(code, language);

    // حفظ التحليل في قاعدة البيانات إذا تم توفير معرف المشروع
    if (projectId) {
        try {
            await db.saveCodeAnalysis(projectId, 'temp_file', code, language, { suggestions: analysis }, analysis);
            await db.logActivity(projectId, 'code_analysis', { language, codeLength: code.length });
        } catch (error) {
            console.error('خطأ في حفظ التحليل:', error.message);
        }
    }

    return analysis;
}

// دالة ترجمة التعليقات مع قاعدة البيانات
async function translateCommentWithDB(text, targetLanguage) {
    const sourceLang = targetLanguage === 'ar' ? 'en' : 'ar';

    try {
        // البحث عن ترجمة موجودة
        const existing = await db.findTranslation(text, sourceLang, targetLanguage);
        if (existing.success && existing.data.length > 0) {
            return `ترجمة محفوظة: ${existing.data[0].translated_text}`;
        }

        // إنشاء ترجمة جديدة
        const translation = translateComment(text, targetLanguage);
        const translatedText = translation.split('\n')[1] || text; // استخراج النص المترجم

        // حفظ الترجمة
        await db.saveTranslation(text, translatedText, sourceLang, targetLanguage, 'comment');

        return translation;
    } catch (error) {
        console.error('خطأ في الترجمة:', error.message);
        return translateComment(text, targetLanguage);
    }
}

// دالة حفظ التحليل
async function saveAnalysisToDB(args) {
    try {
        const result = await db.saveCodeAnalysis(
            args.projectId,
            args.filePath,
            args.code,
            args.language,
            args.analysis,
            JSON.stringify(args.analysis)
        );

        if (result.success) {
            return '✅ تم حفظ التحليل بنجاح في قاعدة البيانات';
        } else {
            return `❌ خطأ في حفظ التحليل: ${result.error}`;
        }
    } catch (error) {
        return `❌ خطأ في حفظ التحليل: ${error.message}`;
    }
}

// دالة الحصول على إحصائيات المشروع
async function getProjectStatsFromDB(projectId) {
    try {
        const result = await db.getProjectStats(projectId);

        if (result.success && result.data.length > 0) {
            const stats = result.data[0];
            return `📊 إحصائيات المشروع ${projectId}:
• إجمالي التحليلات: ${stats.total_analyses}
• عدد اللغات: ${stats.languages_count}
• آخر تحليل: ${stats.last_analysis || 'لا يوجد'}`;
        } else {
            return `❌ لم يتم العثور على إحصائيات للمشروع ${projectId}`;
        }
    } catch (error) {
        return `❌ خطأ في الحصول على الإحصائيات: ${error.message}`;
    }
}

// بدء الخادم مع اختبار قاعدة البيانات
async function startServer() {
    // اختبار الاتصال بقاعدة البيانات
    const dbConnected = await db.testConnection();

    app.listen(port, () => {
        console.log(`🚀 خادم MCP يعمل على http://localhost:${port}`);
        console.log(`📋 للتحقق من الحالة: http://localhost:${port}/health`);
        console.log(`🗄️ قاعدة البيانات: ${dbConnected ? 'متصلة ✅' : 'غير متصلة ❌'}`);
    });
}

// بدء الخادم
startServer().catch(console.error);

module.exports = app;
