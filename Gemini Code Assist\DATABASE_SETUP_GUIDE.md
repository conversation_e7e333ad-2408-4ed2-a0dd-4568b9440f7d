# دليل إعداد قاعدة البيانات - Gemini Code Assist

## الطريقة الأولى: استخدام MySQL Workbench (الأسهل)

### الخطوات:

1. **افتح MySQL Workbench**
   - تأكد من أنك متصل بخادم MySQL المحلي

2. **افتح ملف SQL**
   - اذهب إلى `File` → `Open SQL Script`
   - اختر الملف: `database/create_database_workbench.sql`

3. **تنفيذ السكريبت**
   - اضغط على أيقونة البرق ⚡ أو `Ctrl+Shift+Enter`
   - انتظر حتى يكتمل التنفيذ

4. **التحقق من النتائج**
   - ستظهر رسائل تأكيد إنشاء قاعدة البيانات
   - ستجد قاعدة البيانات `gemini_code_assist` في قائمة SCHEMAS

### ما سيتم إنشاؤه:

#### الجداول:
- `projects` - معلومات المشاريع
- `code_analysis` - نتائج تحليل الكود
- `translations` - ترجمات النصوص
- `activity_log` - سجل الأنشطة
- `user_settings` - إعدادات المستخدم

#### البيانات التجريبية:
- 4 مشاريع تجريبية
- 6 إعدادات افتراضية
- 40+ ترجمة للمصطلحات البرمجية

---

## الطريقة الثانية: استخدام سطر الأوامر

### إعداد كلمة المرور:

1. **إنشاء ملف .env**
   ```bash
   cp tools/.env.example tools/.env
   ```

2. **تحرير ملف .env**
   - افتح `tools/.env`
   - أضف كلمة مرور MySQL:
   ```
   DB_PASSWORD=your_mysql_password
   ```

3. **تشغيل سكريبت الإعداد**
   ```bash
   npm run setup-db
   ```

---

## اختبار الاتصال

بعد إنشاء قاعدة البيانات، يمكنك اختبار الاتصال:

### 1. تشغيل خادم MCP
```bash
cd tools
npm start
```

### 2. اختبار الأدوات الجديدة

#### اختبار حفظ التحليل:
```bash
curl -X POST http://localhost:3001/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "method": "tools/call",
    "params": {
      "name": "save_analysis",
      "arguments": {
        "projectId": 1,
        "filePath": "test.js",
        "code": "console.log(\"Hello World\");",
        "language": "javascript",
        "analysis": {"suggestions": ["Remove console.log"]}
      }
    }
  }'
```

#### اختبار إحصائيات المشروع:
```bash
curl -X POST http://localhost:3001/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "method": "tools/call",
    "params": {
      "name": "get_project_stats",
      "arguments": {
        "projectId": 1
      }
    }
  }'
```

---

## استكشاف الأخطاء

### خطأ في الاتصال:
- تأكد من أن MySQL يعمل
- تحقق من كلمة المرور في ملف .env
- تأكد من أن المنفذ 3306 متاح

### خطأ في الأذونات:
- تأكد من أن المستخدم له صلاحيات إنشاء قواعد البيانات
- جرب استخدام مستخدم root

### خطأ في الترميز:
- تأكد من أن MySQL يدعم utf8mb4
- تحقق من إعدادات الترميز في MySQL

---

## الخطوات التالية

1. **تشغيل خادم MCP مع قاعدة البيانات**
2. **اختبار الأدوات الجديدة**
3. **إعداد VS Code للاستخدام مع الأدوات**
4. **إضافة المزيد من الأدوات المخصصة**

---

## ملاحظات مهمة

- قاعدة البيانات تستخدم ترميز UTF-8 لدعم النصوص العربية
- جميع الجداول تحتوي على فهارس محسنة للأداء
- البيانات التجريبية تساعد في اختبار الأدوات
- يمكن إضافة المزيد من الترجمات والمشاريع لاحقاً
