/**
 * إعدادات قاعدة البيانات
 * Database configuration for Gemini Code Assist
 */

const mysql = require('mysql2/promise');

// تحميل متغيرات البيئة
require('dotenv').config();

// إعدادات الاتصال بقاعدة البيانات
const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '2452329511',
    database: process.env.DB_NAME || 'gemini_code_assist',
    charset: 'utf8mb4',
    timezone: '+00:00'
};

// إنشاء pool للاتصالات
const pool = mysql.createPool({
    ...dbConfig,
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

/**
 * اختبار الاتصال بقاعدة البيانات
 */
async function testConnection() {
    try {
        const connection = await pool.getConnection();
        await connection.ping();
        connection.release();
        console.log('✅ تم الاتصال بقاعدة البيانات بنجاح');
        return true;
    } catch (error) {
        console.error('❌ خطأ في الاتصال بقاعدة البيانات:', error.message);
        return false;
    }
}

/**
 * تنفيذ استعلام SQL
 */
async function executeQuery(sql, params = []) {
    try {
        const [results] = await pool.execute(sql, params);
        return { success: true, data: results };
    } catch (error) {
        console.error('خطأ في تنفيذ الاستعلام:', error.message);
        return { success: false, error: error.message };
    }
}

/**
 * حفظ تحليل الكود في قاعدة البيانات
 */
async function saveCodeAnalysis(projectId, filePath, code, language, analysis, suggestions) {
    const sql = `
        INSERT INTO code_analysis (project_id, file_path, code_content, language, analysis_result, suggestions)
        VALUES (?, ?, ?, ?, ?, ?)
    `;
    const params = [projectId, filePath, code, language, JSON.stringify(analysis), suggestions];
    return await executeQuery(sql, params);
}

/**
 * حفظ الترجمة في قاعدة البيانات
 */
async function saveTranslation(originalText, translatedText, sourceLang, targetLang, context = null) {
    // البحث عن ترجمة موجودة
    const searchSql = `
        SELECT id FROM translations 
        WHERE original_text = ? AND source_language = ? AND target_language = ?
        LIMIT 1
    `;
    const existing = await executeQuery(searchSql, [originalText, sourceLang, targetLang]);
    
    if (existing.success && existing.data.length > 0) {
        // تحديث الترجمة الموجودة
        const updateSql = `
            UPDATE translations 
            SET translated_text = ?, context = ?
            WHERE id = ?
        `;
        return await executeQuery(updateSql, [translatedText, context, existing.data[0].id]);
    } else {
        // إدراج ترجمة جديدة
        const insertSql = `
            INSERT INTO translations (original_text, translated_text, source_language, target_language, context)
            VALUES (?, ?, ?, ?, ?)
        `;
        return await executeQuery(insertSql, [originalText, translatedText, sourceLang, targetLang, context]);
    }
}

/**
 * البحث عن ترجمة موجودة
 */
async function findTranslation(text, sourceLang, targetLang) {
    const sql = `
        SELECT translated_text, context 
        FROM translations 
        WHERE original_text = ? AND source_language = ? AND target_language = ?
        ORDER BY created_at DESC
        LIMIT 1
    `;
    return await executeQuery(sql, [text, sourceLang, targetLang]);
}

/**
 * الحصول على إحصائيات المشروع
 */
async function getProjectStats(projectId) {
    const sql = `
        SELECT 
            COUNT(*) as total_analyses,
            COUNT(DISTINCT language) as languages_count,
            MAX(created_at) as last_analysis
        FROM code_analysis 
        WHERE project_id = ?
    `;
    return await executeQuery(sql, [projectId]);
}

/**
 * تسجيل نشاط في السجل
 */
async function logActivity(projectId, action, details, userAgent = null, ipAddress = null) {
    const sql = `
        INSERT INTO activity_log (project_id, action, details, user_agent, ip_address)
        VALUES (?, ?, ?, ?, ?)
    `;
    const params = [projectId, action, JSON.stringify(details), userAgent, ipAddress];
    return await executeQuery(sql, params);
}

/**
 * الحصول على إعداد مستخدم
 */
async function getUserSetting(key) {
    const sql = `SELECT setting_value FROM user_settings WHERE setting_key = ?`;
    const result = await executeQuery(sql, [key]);
    if (result.success && result.data.length > 0) {
        return JSON.parse(result.data[0].setting_value);
    }
    return null;
}

/**
 * تحديث إعداد مستخدم
 */
async function updateUserSetting(key, value) {
    const sql = `
        INSERT INTO user_settings (setting_key, setting_value) 
        VALUES (?, ?) 
        ON DUPLICATE KEY UPDATE 
        setting_value = VALUES(setting_value),
        updated_at = CURRENT_TIMESTAMP
    `;
    return await executeQuery(sql, [key, JSON.stringify(value)]);
}

module.exports = {
    pool,
    testConnection,
    executeQuery,
    saveCodeAnalysis,
    saveTranslation,
    findTranslation,
    getProjectStats,
    logActivity,
    getUserSetting,
    updateUserSetting
};
