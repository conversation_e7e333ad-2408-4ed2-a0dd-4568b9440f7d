{"name": "gemini-code-assist-tools", "version": "1.0.0", "description": "أدوات مخصصة لـ Gemini Code Assist", "main": "simple-mcp-server.js", "scripts": {"start": "node simple-mcp-server.js", "dev": "nodemon simple-mcp-server.js", "setup-db": "node ../scripts/setup-database.js", "test": "echo \"لا توجد اختبارات محددة حالياً\" && exit 1"}, "keywords": ["gemini", "code-assist", "mcp", "tools", "arabic"], "author": "مطور عربي", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.14.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}