-- ========================================
-- إنشاء قاعدة بيانات Gemini Code Assist
-- يمكن تشغيل هذا السكريبت مباشرة في MySQL Workbench
-- ========================================

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS gemini_code_assist 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- استخدام قاعدة البيانات
USE gemini_code_assist;

-- ========================================
-- إنشاء الجداول
-- ========================================

-- جدول المشاريع
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL COMMENT 'اسم المشروع',
    description TEXT COMMENT 'وصف المشروع',
    language VARCHAR(50) DEFAULT 'javascript' COMMENT 'لغة البرمجة الأساسية',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'archived') DEFAULT 'active' COMMENT 'حالة المشروع'
);

-- جدول تحليل الكود
CREATE TABLE IF NOT EXISTS code_analysis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT,
    file_path VARCHAR(500) NOT NULL COMMENT 'مسار الملف',
    code_content TEXT NOT NULL COMMENT 'محتوى الكود',
    language VARCHAR(50) NOT NULL COMMENT 'لغة البرمجة',
    analysis_result JSON COMMENT 'نتائج التحليل',
    suggestions TEXT COMMENT 'الاقتراحات',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    INDEX idx_project_file (project_id, file_path(255)),
    INDEX idx_language (language),
    INDEX idx_created_at (created_at)
);

-- جدول الترجمات
CREATE TABLE IF NOT EXISTS translations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    original_text TEXT NOT NULL COMMENT 'النص الأصلي',
    translated_text TEXT NOT NULL COMMENT 'النص المترجم',
    source_language VARCHAR(10) NOT NULL COMMENT 'اللغة المصدر',
    target_language VARCHAR(10) NOT NULL COMMENT 'اللغة المستهدفة',
    context VARCHAR(100) COMMENT 'السياق (comment, variable, function, etc.)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_languages (source_language, target_language),
    INDEX idx_context (context),
    FULLTEXT idx_original_text (original_text),
    FULLTEXT idx_translated_text (translated_text)
);

-- جدول سجل الأنشطة
CREATE TABLE IF NOT EXISTS activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT,
    action VARCHAR(100) NOT NULL COMMENT 'نوع العملية',
    details JSON COMMENT 'تفاصيل العملية',
    user_agent VARCHAR(500) COMMENT 'معلومات المستخدم',
    ip_address VARCHAR(45) COMMENT 'عنوان IP',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL,
    INDEX idx_project_action (project_id, action),
    INDEX idx_created_at (created_at)
);

-- جدول إعدادات المستخدم
CREATE TABLE IF NOT EXISTS user_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE COMMENT 'مفتاح الإعداد',
    setting_value JSON NOT NULL COMMENT 'قيمة الإعداد',
    description TEXT COMMENT 'وصف الإعداد',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- ========================================
-- إدراج البيانات التجريبية
-- ========================================

-- إدراج مشاريع تجريبية
INSERT INTO projects (name, description, language) VALUES
('مشروع Gemini Code Assist', 'مشروع لاختبار أدوات Gemini Code Assist مع دعم العربية', 'javascript'),
('تطبيق ويب Node.js', 'تطبيق ويب بـ Node.js و Express مع MCP', 'javascript'),
('مكتبة Python للذكاء الاصطناعي', 'مكتبة Python لمعالجة البيانات والذكاء الاصطناعي', 'python'),
('تطبيق React', 'تطبيق React مع TypeScript', 'typescript');

-- إدراج إعدادات افتراضية
INSERT INTO user_settings (setting_key, setting_value, description) VALUES
('preferred_language', '"ar"', 'اللغة المفضلة للواجهة'),
('analysis_level', '"detailed"', 'مستوى التحليل المفضل'),
('auto_translate', 'true', 'الترجمة التلقائية للتعليقات'),
('max_suggestions', '10', 'الحد الأقصى لعدد الاقتراحات'),
('theme', '"dark"', 'سمة الواجهة'),
('auto_save', 'true', 'الحفظ التلقائي للتحليلات');

-- إدراج ترجمات شائعة للبرمجة
INSERT INTO translations (original_text, translated_text, source_language, target_language, context) VALUES
-- كلمات مفتاحية أساسية
('function', 'دالة', 'en', 'ar', 'keyword'),
('variable', 'متغير', 'en', 'ar', 'keyword'),
('class', 'فئة', 'en', 'ar', 'keyword'),
('method', 'طريقة', 'en', 'ar', 'keyword'),
('return', 'إرجاع', 'en', 'ar', 'keyword'),
('parameter', 'معامل', 'en', 'ar', 'keyword'),
('argument', 'وسيطة', 'en', 'ar', 'keyword'),
('array', 'مصفوفة', 'en', 'ar', 'keyword'),
('object', 'كائن', 'en', 'ar', 'keyword'),
('string', 'نص', 'en', 'ar', 'keyword'),
('number', 'رقم', 'en', 'ar', 'keyword'),
('boolean', 'منطقي', 'en', 'ar', 'keyword'),
('null', 'فارغ', 'en', 'ar', 'keyword'),
('undefined', 'غير محدد', 'en', 'ar', 'keyword'),
('const', 'ثابت', 'en', 'ar', 'keyword'),
('let', 'متغير', 'en', 'ar', 'keyword'),
('var', 'متغير', 'en', 'ar', 'keyword'),
('if', 'إذا', 'en', 'ar', 'keyword'),
('else', 'وإلا', 'en', 'ar', 'keyword'),
('for', 'لكل', 'en', 'ar', 'keyword'),
('while', 'بينما', 'en', 'ar', 'keyword'),
('try', 'جرب', 'en', 'ar', 'keyword'),
('catch', 'اعترض', 'en', 'ar', 'keyword'),
('finally', 'أخيراً', 'en', 'ar', 'keyword'),
('import', 'استيراد', 'en', 'ar', 'keyword'),
('export', 'تصدير', 'en', 'ar', 'keyword'),
('async', 'غير متزامن', 'en', 'ar', 'keyword'),
('await', 'انتظار', 'en', 'ar', 'keyword'),

-- مصطلحات البرمجة
('loop', 'حلقة', 'en', 'ar', 'concept'),
('condition', 'شرط', 'en', 'ar', 'concept'),
('iteration', 'تكرار', 'en', 'ar', 'concept'),
('callback', 'استدعاء عكسي', 'en', 'ar', 'concept'),
('promise', 'وعد', 'en', 'ar', 'concept'),
('error', 'خطأ', 'en', 'ar', 'concept'),
('exception', 'استثناء', 'en', 'ar', 'concept'),
('debug', 'تصحيح', 'en', 'ar', 'concept'),
('test', 'اختبار', 'en', 'ar', 'concept'),
('module', 'وحدة', 'en', 'ar', 'concept'),
('component', 'مكون', 'en', 'ar', 'concept'),
('interface', 'واجهة', 'en', 'ar', 'concept'),
('database', 'قاعدة بيانات', 'en', 'ar', 'concept'),
('server', 'خادم', 'en', 'ar', 'concept'),
('client', 'عميل', 'en', 'ar', 'concept'),
('API', 'واجهة برمجة التطبيقات', 'en', 'ar', 'concept'),
('framework', 'إطار عمل', 'en', 'ar', 'concept'),
('library', 'مكتبة', 'en', 'ar', 'concept');

-- ========================================
-- عرض معلومات قاعدة البيانات
-- ========================================

-- عرض الجداول المنشأة
SELECT 'تم إنشاء قاعدة البيانات بنجاح!' as status;

SELECT 
    'إحصائيات قاعدة البيانات' as info,
    (SELECT COUNT(*) FROM projects) as projects_count,
    (SELECT COUNT(*) FROM translations) as translations_count,
    (SELECT COUNT(*) FROM user_settings) as settings_count;

-- عرض المشاريع
SELECT 'المشاريع المنشأة:' as info;
SELECT id, name, language, status FROM projects;

-- عرض الإعدادات
SELECT 'الإعدادات الافتراضية:' as info;
SELECT setting_key, setting_value, description FROM user_settings;

SELECT 'تم الانتهاء من إعداد قاعدة البيانات!' as final_status;
